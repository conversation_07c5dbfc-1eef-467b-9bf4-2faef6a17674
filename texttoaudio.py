import pyttsx3


def text_to_speech(text):
    # 初始化 TTS 引擎
    engine = pyttsx3.init()

    # 可选：设置语速和音量
    engine.setProperty('rate', 150)  # 设置语速，默认为 200
    engine.setProperty('volume', 0.9)  # 设置音量，范围是 0.0 到 1.0

    # 说出文本
    engine.say(text)
    engine.runAndWait()


def save_speech_to_wav(text, output_file='output.wav'):
    engine = pyttsx3.init()
    engine.setProperty('rate', 150)
    engine.setProperty('volume', 1.0)

    # 保存语音为 WAV 文件
    engine.save_to_file(text, output_file)
    engine.runAndWait()
    print(f"WAV文件已保存为：{output_file}")

def speak_with_male_voice(text):
    engine = pyttsx3.init()
    voices = engine.getProperty('voices')

    # 查找第一个包含“male”或名字中看似为男声的条目
    male_voice = None
    for voice in voices:
        if 'male' in voice.name.lower() or 'zh_cn' in voice.id.lower():
            male_voice = voice.id
            break

    if male_voice:
        engine.setProperty('voice', male_voice)
    else:
        print("未找到男声语音，引擎将使用默认语音。")

    engine.say(text)
    engine.runAndWait()
def list_voices():
    engine = pyttsx3.init()
    voices = engine.getProperty('voices')
    for index, voice in enumerate(voices):
        print(f"Voice {index}:")
        print(f" - ID: {voice.id}")
        print(f" - Name: {voice.name}")
        print(f" - Gender: {voice.gender if hasattr(voice, 'gender') else 'Unknown'}")
        print(f" - Languages: {voice.languages}\n")

if __name__ == '__main__':
    # sample_text = "hello world。"
    # save_speech_to_wav(sample_text)
    # text_to_speech(sample_text)
    list_voices()